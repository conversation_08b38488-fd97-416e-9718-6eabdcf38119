#!/bin/bash

# Start both Piper TTS API and React frontend

echo "🚀 Starting Piper TTS Services..."
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $API_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start the API server in background
echo "📡 Starting Piper TTS API server..."
cd "$(dirname "$0")"
source .venv/bin/activate
python run_piper_api.py &
API_PID=$!

# Wait a moment for API to start
sleep 3

# Start the React frontend in background
echo "🌐 Starting React frontend..."
cd piper_frontend
npm start &
FRONTEND_PID=$!

echo ""
echo "✅ Services started successfully!"
echo ""
echo "🔗 API Server: http://localhost:8000"
echo "🔗 API Docs: http://localhost:8000/docs"
echo "🔗 Frontend: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for both processes
wait $API_PID $FRONTEND_PID
