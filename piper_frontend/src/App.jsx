import { useState, useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';

function App() {
  const [text, setText] = useState('नेपाल एक सुन्दर हिमालयी देश हो जहाँ विविधताले भरिएको संस्कृति र परम्परा छ। यहाँका मानिसहरू धेरै मिलनसार र दयालु छन्। नेपालमा अनेकौं भाषा र जातिका मानिसहरू एकसाथ बस्छन्।');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.0);
  const [isLoading, setIsLoading] = useState(false);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [apiStatus, setApiStatus] = useState('checking');
  const [audioUrl, setAudioUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [fontSize, setFontSize] = useState(16);

  const waveformRef = useRef(null);
  const wavesurfer = useRef(null);

  const API_BASE = 'http://localhost:8000';

  // Check API status and load voice info
  useEffect(() => {
    checkApiStatus();
    loadVoiceInfo();
    loadSpeakers();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/`);
      const data = await response.json();
      setApiStatus(data.voice_loaded ? 'ready' : 'no-voice');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadVoiceInfo = async () => {
    try {
      const response = await fetch(`${API_BASE}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        setVoiceInfo(data);
      }
    } catch (error) {
      console.error('Failed to load voice info:', error);
    }
  };

  const loadSpeakers = async () => {
    try {
      const response = await fetch(`${API_BASE}/speakers`);
      if (response.ok) {
        const data = await response.json();
        setSpeakers(data.speakers || []);
      }
    } catch (error) {
      console.error('Failed to load speakers:', error);
    }
  };

  const synthesizeSpeech = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    setAudioUrl(null);

    // Destroy existing wavesurfer instance
    if (wavesurfer.current) {
      wavesurfer.current.destroy();
      wavesurfer.current = null;
    }

    try {
      const params = new URLSearchParams({
        text: text,
        speaker_id: speakerId,
        speech_rate: speechRate,
        noise_scale: noiseScale,
        noise_w: noiseW,
        sentence_silence: sentenceSilence
      });

      const response = await fetch(`${API_BASE}/synthesize?${params}`);

      if (response.ok) {
        const audioBlob = await response.blob();
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // Initialize WaveSurfer after audio is ready
        setTimeout(() => initializeWaveSurfer(url), 100);
      } else {
        alert('Failed to synthesize speech');
      }
    } catch (error) {
      console.error('Synthesis error:', error);
      alert('Error connecting to API');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeWaveSurfer = (audioUrl) => {
    if (waveformRef.current && audioUrl) {
      wavesurfer.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#ffffff',
        progressColor: '#6b7280',
        cursorColor: '#ffffff',
        barWidth: 2,
        barRadius: 3,
        responsive: true,
        height: 80,
        normalize: true,
        backend: 'WebAudio',
        mediaControls: false,
      });

      wavesurfer.current.load(audioUrl);

      wavesurfer.current.on('ready', () => {
        setDuration(wavesurfer.current.getDuration());
      });

      wavesurfer.current.on('audioprocess', () => {
        setCurrentTime(wavesurfer.current.getCurrentTime());
      });

      wavesurfer.current.on('play', () => {
        setIsPlaying(true);
      });

      wavesurfer.current.on('pause', () => {
        setIsPlaying(false);
      });

      wavesurfer.current.on('finish', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });
    }
  };

  const togglePlayPause = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (apiStatus) {
      case 'ready': return 'text-green-600';
      case 'no-voice': return 'text-yellow-600';
      case 'offline': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'ready': return 'API Ready';
      case 'no-voice': return 'No Voice Loaded';
      case 'offline': return 'API Offline';
      default: return 'Checking...';
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-800 overflow-hidden relative">
      {/* Animated Background with Flowing Dots */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {/* Flowing liquid effect */}
        <div className="absolute inset-0">
          <div className="flowing-liquid"></div>
          <div className="flowing-liquid-2"></div>
        </div>

        {/* Animated dots with different sizes */}
        <div className="floating-dots">
          {/* Small dots */}
          {[...Array(15)].map((_, i) => (
            <div
              key={`small-${i}`}
              className="floating-dot small"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 20}s`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            ></div>
          ))}

          {/* Medium dots */}
          {[...Array(10)].map((_, i) => (
            <div
              key={`medium-${i}`}
              className="floating-dot medium"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 25}s`,
                animationDuration: `${20 + Math.random() * 15}s`
              }}
            ></div>
          ))}

          {/* Large dots */}
          {[...Array(5)].map((_, i) => (
            <div
              key={`large-${i}`}
              className="floating-dot large"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 30}s`,
                animationDuration: `${25 + Math.random() * 20}s`
              }}
            ></div>
          ))}
        </div>
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="text-center py-4 border-b border-gray-300/50">
          <h1 className="text-3xl font-light text-gray-800 mb-1 tracking-wider">
            🎤 PIPER TTS
          </h1>
          <div className="flex items-center justify-center space-x-6">
            <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
              <div className={`w-2 h-2 rounded-full ${apiStatus === 'ready' ? 'bg-green-500 animate-pulse' : apiStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'}`}></div>
              <span className="text-sm font-medium tracking-wide text-gray-700">{getStatusText()}</span>
            </div>
            {voiceInfo && (
              <div className="text-gray-600 text-sm">
                {voiceInfo.num_speakers} speakers • {voiceInfo.sample_rate}Hz
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 grid grid-cols-1 lg:grid-cols-3 gap-4 min-h-0">
          {/* Left Panel - Text Input */}
          <div className="lg:col-span-2 flex flex-col">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4 flex-1 flex flex-col shadow-lg glass-panel">
              <h2 className="text-lg font-medium text-gray-800 mb-3 tracking-wide">Text Input</h2>

              <div className="flex-1 flex flex-col space-y-3">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700 tracking-wide">
                      TEXT TO SYNTHESIZE
                    </label>
                    <div className="flex items-center space-x-2">
                      <label className="text-xs text-gray-600">Font Size:</label>
                      <input
                        type="range"
                        min="12"
                        max="20"
                        value={fontSize}
                        onChange={(e) => setFontSize(parseInt(e.target.value))}
                        className="w-16 h-1 bg-gray-300 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <span className="text-xs text-gray-600 w-8">{fontSize}px</span>
                    </div>
                  </div>
                  <textarea
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    style={{ fontSize: `${fontSize}px` }}
                    className="w-full h-32 p-3 bg-white/30 backdrop-blur-sm border border-white/40 rounded-lg text-gray-800 placeholder-gray-500 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 resize-none shadow-lg"
                    placeholder="नेपाली पाठ यहाँ लेख्नुहोस्..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 tracking-wide">
                      SPEAKER ({speakers.length} available)
                    </label>
                    <select
                      value={speakerId}
                      onChange={(e) => setSpeakerId(parseInt(e.target.value))}
                      className="w-full p-2 bg-white/30 backdrop-blur-sm border border-white/40 rounded-lg text-gray-800 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 shadow-lg"
                    >
                      {speakers.map((speaker) => (
                        <option key={speaker.speaker_id} value={speaker.speaker_id} className="bg-white">
                          Speaker {speaker.speaker_id}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 tracking-wide">
                      SPEECH RATE: {speechRate}x
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={speechRate}
                      onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                      className="w-full h-2 bg-gray-300/50 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>

                {/* Generate Button */}
                <button
                  onClick={synthesizeSpeech}
                  disabled={isLoading || apiStatus !== 'ready' || !text.trim()}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Synthesizing...</span>
                    </div>
                  ) : (
                    '🎵 Generate Speech'
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Right Panel - Controls and Audio */}
          <div className="flex flex-col space-y-4">
            {/* Advanced Controls */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4 shadow-lg glass-panel">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium text-gray-800 tracking-wide">ADVANCED</h3>
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="text-gray-600 hover:text-gray-800 transition-colors duration-200"
                >
                  <svg className={`w-4 h-4 transform transition-transform duration-200 ${showAdvanced ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              {showAdvanced && (
                <div className="space-y-3 animate-fade-in">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1 tracking-wider">
                      NOISE SCALE: {noiseScale.toFixed(2)}
                    </label>
                    <input
                      type="range"
                      min="0.0"
                      max="1.0"
                      step="0.01"
                      value={noiseScale}
                      onChange={(e) => setNoiseScale(parseFloat(e.target.value))}
                      className="w-full h-1 bg-gray-300 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1 tracking-wider">
                      PHONEME WIDTH: {noiseW.toFixed(2)}
                    </label>
                    <input
                      type="range"
                      min="0.0"
                      max="1.0"
                      step="0.01"
                      value={noiseW}
                      onChange={(e) => setNoiseW(parseFloat(e.target.value))}
                      className="w-full h-1 bg-gray-300 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1 tracking-wider">
                      SENTENCE SILENCE: {sentenceSilence.toFixed(1)}s
                    </label>
                    <input
                      type="range"
                      min="0.0"
                      max="2.0"
                      step="0.1"
                      value={sentenceSilence}
                      onChange={(e) => setSentenceSilence(parseFloat(e.target.value))}
                      className="w-full h-1 bg-gray-300 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Audio Visualization */}
            {audioUrl && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4 animate-fade-in shadow-lg glass-panel">
                <h3 className="text-lg font-medium text-gray-800 mb-3 tracking-wide">Audio Player</h3>

                {/* Waveform */}
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 mb-4">
                  <div ref={waveformRef} className="w-full"></div>
                </div>

                {/* Audio Controls */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={togglePlayPause}
                    className="flex items-center justify-center w-10 h-10 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-all duration-300"
                  >
                    {isPlaying ? (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                      </svg>
                    ) : (
                      <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    )}
                  </button>

                  <div className="flex-1">
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>{formatTime(currentTime)}</span>
                      <span>{formatTime(duration)}</span>
                    </div>
                    <div className="w-full bg-gray-300 rounded-full h-1">
                      <div
                        className="bg-blue-500 h-1 rounded-full transition-all duration-100"
                        style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>

                  <a
                    href={audioUrl}
                    download="piper_tts_output.wav"
                    className="flex items-center space-x-1 bg-gray-600 text-white py-2 px-3 rounded-lg hover:bg-gray-700 transition-all duration-300"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="text-xs">DL</span>
                  </a>
                </div>
              </div>
            )}

            {/* Sample Texts */}
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4 flex-1 overflow-y-auto shadow-lg glass-panel">
              <h3 className="text-lg font-medium text-gray-800 mb-3 tracking-wide">Sample Texts</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setText('नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ। म नेपाली भाषामा बोल्न सक्छु र विभिन्न प्रकारका पाठहरूलाई प्राकृतिक आवाजमा रूपान्तरण गर्न सक्छु।')}
                  className="w-full p-3 bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/40 rounded-lg text-left transition-all duration-300"
                >
                  <div className="font-medium text-gray-800 text-sm mb-1">AI Introduction</div>
                  <div className="text-xs text-gray-600 line-clamp-2">नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ...</div>
                </button>

                <button
                  onClick={() => setText('हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो। यहाँका पहाडहरू, नदीहरू र जंगलहरूले प्राकृतिक सुन्दरताको अनुपम दृश्य प्रस्तुत गर्छन्।')}
                  className="w-full p-3 bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/40 rounded-lg text-left transition-all duration-300"
                >
                  <div className="font-medium text-gray-800 text-sm mb-1">Nepal</div>
                  <div className="text-xs text-gray-600 line-clamp-2">हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो...</div>
                </button>

                <button
                  onClick={() => setText('प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ। आजकल धेरै युवाहरू सफ्टवेयर विकास र डिजिटल मार्केटिङमा काम गरिरहेका छन्।')}
                  className="w-full p-3 bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/40 rounded-lg text-left transition-all duration-300"
                >
                  <div className="font-medium text-gray-800 text-sm mb-1">Technology</div>
                  <div className="text-xs text-gray-600 line-clamp-2">प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ...</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
