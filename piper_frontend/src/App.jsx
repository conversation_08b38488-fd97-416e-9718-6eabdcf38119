import { useState, useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';

function App() {
  const [text, setText] = useState('नेपाल एक सुन्दर हिमालयी देश हो जहाँ विविधताले भरिएको संस्कृति र परम्परा छ। यहाँका मानिसहरू धेरै मिलनसार र दयालु छन्। नेपालमा अनेकौं भाषा र जातिका मानिसहरू एकसाथ बस्छन्।');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.0);
  const [isLoading, setIsLoading] = useState(false);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [apiStatus, setApiStatus] = useState('checking');
  const [audioUrl, setAudioUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const waveformRef = useRef(null);
  const wavesurfer = useRef(null);

  const API_BASE = 'http://localhost:8000';

  // Check API status and load voice info
  useEffect(() => {
    checkApiStatus();
    loadVoiceInfo();
    loadSpeakers();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/`);
      const data = await response.json();
      setApiStatus(data.voice_loaded ? 'ready' : 'no-voice');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const loadVoiceInfo = async () => {
    try {
      const response = await fetch(`${API_BASE}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        setVoiceInfo(data);
      }
    } catch (error) {
      console.error('Failed to load voice info:', error);
    }
  };

  const loadSpeakers = async () => {
    try {
      const response = await fetch(`${API_BASE}/speakers`);
      if (response.ok) {
        const data = await response.json();
        setSpeakers(data.speakers || []);
      }
    } catch (error) {
      console.error('Failed to load speakers:', error);
    }
  };

  const synthesizeSpeech = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    setAudioUrl(null);

    // Destroy existing wavesurfer instance
    if (wavesurfer.current) {
      wavesurfer.current.destroy();
      wavesurfer.current = null;
    }

    try {
      const params = new URLSearchParams({
        text: text,
        speaker_id: speakerId,
        speech_rate: speechRate,
        noise_scale: noiseScale,
        noise_w: noiseW,
        sentence_silence: sentenceSilence
      });

      const response = await fetch(`${API_BASE}/synthesize?${params}`);

      if (response.ok) {
        const audioBlob = await response.blob();
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);

        // Initialize WaveSurfer after audio is ready
        setTimeout(() => initializeWaveSurfer(url), 100);
      } else {
        alert('Failed to synthesize speech');
      }
    } catch (error) {
      console.error('Synthesis error:', error);
      alert('Error connecting to API');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeWaveSurfer = (audioUrl) => {
    if (waveformRef.current && audioUrl) {
      wavesurfer.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#ffffff',
        progressColor: '#6b7280',
        cursorColor: '#ffffff',
        barWidth: 2,
        barRadius: 3,
        responsive: true,
        height: 80,
        normalize: true,
        backend: 'WebAudio',
        mediaControls: false,
      });

      wavesurfer.current.load(audioUrl);

      wavesurfer.current.on('ready', () => {
        setDuration(wavesurfer.current.getDuration());
      });

      wavesurfer.current.on('audioprocess', () => {
        setCurrentTime(wavesurfer.current.getCurrentTime());
      });

      wavesurfer.current.on('play', () => {
        setIsPlaying(true);
      });

      wavesurfer.current.on('pause', () => {
        setIsPlaying(false);
      });

      wavesurfer.current.on('finish', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });
    }
  };

  const togglePlayPause = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (apiStatus) {
      case 'ready': return 'text-green-600';
      case 'no-voice': return 'text-yellow-600';
      case 'offline': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'ready': return 'API Ready';
      case 'no-voice': return 'No Voice Loaded';
      case 'offline': return 'API Offline';
      default: return 'Checking...';
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white opacity-5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gray-400 opacity-10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>
      </div>

      <div className="relative z-10 min-h-screen py-8">
        <div className="max-w-6xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-6xl font-thin text-white mb-4 tracking-wider animate-fade-in">
              PIPER
            </h1>
            <div className="w-24 h-0.5 bg-white mx-auto mb-6 animate-expand"></div>
            <p className="text-xl text-gray-300 font-light tracking-wide">
              Neural Text-to-Speech Synthesis
            </p>
            <div className="flex items-center justify-center space-x-6 mt-6">
              <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
                <div className={`w-2 h-2 rounded-full ${apiStatus === 'ready' ? 'bg-green-400 animate-pulse' : apiStatus === 'offline' ? 'bg-red-400' : 'bg-yellow-400'}`}></div>
                <span className="text-sm font-medium tracking-wide">{getStatusText()}</span>
              </div>
              {voiceInfo && (
                <div className="text-gray-400 text-sm">
                  {voiceInfo.num_speakers} speakers • {voiceInfo.sample_rate}Hz
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Text Input Panel */}
            <div className="xl:col-span-2">
              <div className="bg-gray-900 bg-opacity-50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300">
                <h2 className="text-2xl font-light text-white mb-6 tracking-wide">Text Input</h2>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3 tracking-wide">
                      TEXT TO SYNTHESIZE
                    </label>
                    <textarea
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      className="w-full p-4 bg-black bg-opacity-50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:ring-2 focus:ring-white focus:border-transparent transition-all duration-300 resize-none"
                      rows="6"
                      placeholder="नेपाली पाठ यहाँ लेख्नुहोस्..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-3 tracking-wide">
                        SPEAKER ({speakers.length} available)
                      </label>
                      <select
                        value={speakerId}
                        onChange={(e) => setSpeakerId(parseInt(e.target.value))}
                        className="w-full p-3 bg-black bg-opacity-50 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-white focus:border-transparent transition-all duration-300"
                      >
                        {speakers.map((speaker) => (
                          <option key={speaker.speaker_id} value={speaker.speaker_id} className="bg-black">
                            Speaker {speaker.speaker_id}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-3 tracking-wide">
                        SPEECH RATE: {speechRate}x
                      </label>
                      <input
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={speechRate}
                        onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                  </div>

                  {/* Generate Button */}
                  <button
                    onClick={synthesizeSpeech}
                    disabled={isLoading || apiStatus !== 'ready' || !text.trim()}
                    className="w-full bg-white text-black py-4 px-6 rounded-xl hover:bg-gray-200 disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed font-medium tracking-wide transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                        <span>SYNTHESIZING...</span>
                      </div>
                    ) : (
                      'GENERATE SPEECH'
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Controls Panel */}
            <div className="space-y-6">
              <div className="bg-gray-900 bg-opacity-50 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 hover:border-gray-600 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-light text-white tracking-wide">ADVANCED</h3>
                  <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    <svg className={`w-5 h-5 transform transition-transform duration-200 ${showAdvanced ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>

                {showAdvanced && (
                  <div className="space-y-4 animate-fade-in">
                    <div>
                      <label className="block text-xs font-medium text-gray-400 mb-2 tracking-wider">
                        NOISE SCALE: {noiseScale.toFixed(2)}
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="1.0"
                        step="0.01"
                        value={noiseScale}
                        onChange={(e) => setNoiseScale(parseFloat(e.target.value))}
                        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-400 mb-2 tracking-wider">
                        PHONEME WIDTH: {noiseW.toFixed(2)}
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="1.0"
                        step="0.01"
                        value={noiseW}
                        onChange={(e) => setNoiseW(parseFloat(e.target.value))}
                        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-400 mb-2 tracking-wider">
                        SENTENCE SILENCE: {sentenceSilence.toFixed(1)}s
                      </label>
                      <input
                        type="range"
                        min="0.0"
                        max="2.0"
                        step="0.1"
                        value={sentenceSilence}
                        onChange={(e) => setSentenceSilence(parseFloat(e.target.value))}
                        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Audio Visualization */}
          {audioUrl && (
            <div className="mt-8 animate-fade-in">
              <div className="bg-gray-900 bg-opacity-50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300">
                <h2 className="text-2xl font-light text-white mb-6 tracking-wide">Audio Visualization</h2>

                {/* Waveform */}
                <div className="bg-black bg-opacity-50 rounded-xl p-6 mb-6">
                  <div ref={waveformRef} className="w-full"></div>
                </div>

                {/* Audio Controls */}
                <div className="flex items-center justify-between mb-6">
                  <button
                    onClick={togglePlayPause}
                    className="flex items-center justify-center w-16 h-16 bg-white text-black rounded-full hover:bg-gray-200 transition-all duration-300 transform hover:scale-110"
                  >
                    {isPlaying ? (
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                      </svg>
                    ) : (
                      <svg className="w-6 h-6 ml-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    )}
                  </button>

                  <div className="flex-1 mx-6">
                    <div className="flex justify-between text-sm text-gray-400 mb-1">
                      <span>{formatTime(currentTime)}</span>
                      <span>{formatTime(duration)}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-1">
                      <div
                        className="bg-white h-1 rounded-full transition-all duration-100"
                        style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>

                  <a
                    href={audioUrl}
                    download="piper_tts_output.wav"
                    className="flex items-center space-x-2 bg-gray-800 text-white py-3 px-6 rounded-xl hover:bg-gray-700 transition-all duration-300"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="text-sm tracking-wide">DOWNLOAD</span>
                  </a>
                </div>
              </div>
            </div>
          )}

          {/* Quick Test Examples */}
          <div className="mt-8">
            <div className="bg-gray-900 bg-opacity-50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-gray-600 transition-all duration-300">
              <h2 className="text-2xl font-light text-white mb-6 tracking-wide">Sample Texts</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setText('नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ। म नेपाली भाषामा बोल्न सक्छु र विभिन्न प्रकारका पाठहरूलाई प्राकृतिक आवाजमा रूपान्तरण गर्न सक्छु।')}
                  className="p-4 bg-black bg-opacity-30 hover:bg-opacity-50 border border-gray-600 hover:border-gray-500 rounded-xl text-left transition-all duration-300 group"
                >
                  <div className="font-medium text-white mb-2 group-hover:text-gray-200">AI Introduction</div>
                  <div className="text-sm text-gray-400 line-clamp-3">नमस्ते, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ...</div>
                </button>

                <button
                  onClick={() => setText('हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो। यहाँका पहाडहरू, नदीहरू र जंगलहरूले प्राकृतिक सुन्दरताको अनुपम दृश्य प्रस्तुत गर्छन्। नेपालका मानिसहरू धेरै मिलनसार र अतिथि सत्कार गर्ने स्वभावका छन्।')}
                  className="p-4 bg-black bg-opacity-30 hover:bg-opacity-50 border border-gray-600 hover:border-gray-500 rounded-xl text-left transition-all duration-300 group"
                >
                  <div className="font-medium text-white mb-2 group-hover:text-gray-200">Nepal Description</div>
                  <div className="text-sm text-gray-400 line-clamp-3">हिमालयको काखमा बसेको नेपाल एक सुन्दर देश हो...</div>
                </button>

                <button
                  onClick={() => setText('प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ। आजकल धेरै युवाहरू सफ्टवेयर विकास, वेब डिजाइन र डिजिटल मार्केटिङमा काम गरिरहेका छन्। यसले नेपालको अर्थतन्त्रमा सकारात्मक प्रभाव पारेको छ।')}
                  className="p-4 bg-black bg-opacity-30 hover:bg-opacity-50 border border-gray-600 hover:border-gray-500 rounded-xl text-left transition-all duration-300 group"
                >
                  <div className="font-medium text-white mb-2 group-hover:text-gray-200">Technology</div>
                  <div className="text-sm text-gray-400 line-clamp-3">प्रविधिको क्षेत्रमा नेपालले उल्लेखनीय प्रगति गरेको छ...</div>
                </button>

                <button
                  onClick={() => setText('शिक्षा मानव जीवनको आधारशिला हो। यसले व्यक्तिको व्यक्तित्व विकास गर्छ र समाजको उन्नतिमा योगदान पुर्याउँछ। नेपालमा शिक्षाको क्षेत्रमा निरन्तर सुधार भइरहेको छ र डिजिटल शिक्षाले नयाँ आयाम थपेको छ।')}
                  className="p-4 bg-black bg-opacity-30 hover:bg-opacity-50 border border-gray-600 hover:border-gray-500 rounded-xl text-left transition-all duration-300 group"
                >
                  <div className="font-medium text-white mb-2 group-hover:text-gray-200">Education</div>
                  <div className="text-sm text-gray-400 line-clamp-3">शिक्षा मानव जीवनको आधारशिला हो...</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
