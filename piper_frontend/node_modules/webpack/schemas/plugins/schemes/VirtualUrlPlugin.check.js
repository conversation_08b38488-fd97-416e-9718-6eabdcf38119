/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn fix:special` to update
 */
"use strict";function t(r,{instancePath:e="",parentData:n,parentDataProperty:s,rootData:o=r}={}){let a=null,l=0;const i=l;let p=!1;const u=l;if("string"!=typeof r){const t={params:{type:"string"}};null===a?a=[t]:a.push(t),l++}var c=u===l;if(p=p||c,!p){const t=l;if(!(r instanceof Function)){const t={params:{}};null===a?a=[t]:a.push(t),l++}if(c=t===l,p=p||c,!p){const t=l;if(l==l)if(r&&"object"==typeof r&&!Array.isArray(r)){let t;if(void 0===r.source&&(t="source")){const r={params:{missingProperty:t}};null===a?a=[r]:a.push(r),l++}else{const t=l;for(const t in r)if("source"!==t&&"type"!==t&&"version"!==t){const r={params:{additionalProperty:t}};null===a?a=[r]:a.push(r),l++;break}if(t===l){if(void 0!==r.source){const t=l;if(!(r.source instanceof Function)){const t={params:{}};null===a?a=[t]:a.push(t),l++}var f=t===l}else f=!0;if(f){if(void 0!==r.type){const t=l;if("string"!=typeof r.type){const t={params:{type:"string"}};null===a?a=[t]:a.push(t),l++}f=t===l}else f=!0;if(f)if(void 0!==r.version){let t=r.version;const e=l,n=l;let s=!1;const o=l;if("boolean"!=typeof t){const t={params:{type:"boolean"}};null===a?a=[t]:a.push(t),l++}if(!0!==t){const t={params:{}};null===a?a=[t]:a.push(t),l++}var y=o===l;if(s=s||y,!s){const r=l;if("string"!=typeof t){const t={params:{type:"string"}};null===a?a=[t]:a.push(t),l++}if(y=r===l,s=s||y,!s){const r=l;if(!(t instanceof Function)){const t={params:{}};null===a?a=[t]:a.push(t),l++}y=r===l,s=s||y}}if(s)l=n,null!==a&&(n?a.length=n:a=null);else{const t={params:{}};null===a?a=[t]:a.push(t),l++}f=e===l}else f=!0}}}}else{const t={params:{type:"object"}};null===a?a=[t]:a.push(t),l++}c=t===l,p=p||c}}if(!p){const r={params:{}};return null===a?a=[r]:a.push(r),l++,t.errors=a,!1}return l=i,null!==a&&(i?a.length=i:a=null),t.errors=a,0===l}function r(e,{instancePath:n="",parentData:s,parentDataProperty:o,rootData:a=e}={}){let l=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return r.errors=[{params:{type:"object"}}],!1;{let s;if(void 0===e.modules&&(s="modules"))return r.errors=[{params:{missingProperty:s}}],!1;{const s=i;for(const t in e)if("modules"!==t&&"scheme"!==t)return r.errors=[{params:{additionalProperty:t}}],!1;if(s===i){if(void 0!==e.modules){let s=e.modules;const o=i;if(i===o){if(!s||"object"!=typeof s||Array.isArray(s))return r.errors=[{params:{type:"object"}}],!1;for(const r in s){const e=i;if(t(s[r],{instancePath:n+"/modules/"+r.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:s,parentDataProperty:r,rootData:a})||(l=null===l?t.errors:l.concat(t.errors),i=l.length),e!==i)break}}var p=o===i}else p=!0;if(p)if(void 0!==e.scheme){const t=i;if("string"!=typeof e.scheme)return r.errors=[{params:{type:"string"}}],!1;p=t===i}else p=!0}}}}return r.errors=l,0===i}function e(t,{instancePath:n="",parentData:s,parentDataProperty:o,rootData:a=t}={}){let l=null,i=0;const p=i;let u=!1,c=null;const f=i;if(r(t,{instancePath:n,parentData:s,parentDataProperty:o,rootData:a})||(l=null===l?r.errors:l.concat(r.errors),i=l.length),f===i&&(u=!0,c=0),!u){const t={params:{passingSchemas:c}};return null===l?l=[t]:l.push(t),i++,e.errors=l,!1}return i=p,null!==l&&(p?l.length=p:l=null),e.errors=l,0===i}module.exports=e,module.exports.default=e;