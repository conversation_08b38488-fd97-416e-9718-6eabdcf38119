{"name": "serve-index", "description": "Serve directory listings", "version": "1.9.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "expressjs/serve-index", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "devDependencies": {"after": "0.8.2", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "files": ["public/", "LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}}